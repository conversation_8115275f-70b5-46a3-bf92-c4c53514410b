/**
 * Instagram Video Ads Module
 *
 * Automaticky detekuje Instagram embedy a vkládá video reklamy
 * Inspirováno funkčním YouTube detektorem
 */

(function(window, document) {
    'use strict';

    // Konfigurace
    const defaultConfig = {
        adTagUrl: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&impl=s&correlator=',
        debug: true,
        autoInit: true,
        initDelay: 5000,           // Ještě delší počáteční zpoždění (5 sekund)
        retryInterval: 3000,       // Delší interval pro opakované pokusy (3 sekundy)
        maxRetries: 15,            // <PERSON><PERSON><PERSON> poku<PERSON>ů (15)
        observeNewEmbeds: true,
        waitForInstagramScript: true,  // Čekat na Instagram script
        showManualButton: true     // Zobrazit manuální tlačítko
    };

    let globalConfig = {};
    let imaLoaded = false;
    const players = [];
    let observer = null;
    let retryCount = 0;
    let retryTimer = null;
    let instagramScriptLoaded = false;
    let manualButton = null;

    function log(...args) {
        if (globalConfig.debug) {
            console.log('[Instagram Video Ads]', ...args);
        }
    }

    function error(...args) {
        console.error('[Instagram Video Ads]', ...args);
    }

    /**
     * Inicializace modulu
     */
    function init(userConfig = {}) {
        globalConfig = { ...defaultConfig, ...userConfig };
        log('Inicializace s konfigurací:', globalConfig);

        // Resetujeme retry počítadlo
        retryCount = 0;

        // Načteme IMA SDK
        loadImaScript();

        // Zkontrolujeme Instagram script
        if (globalConfig.waitForInstagramScript) {
            checkInstagramScript();
        }

        // Počkáme na IMA SDK a pak zpracujeme embedy
        checkImaAndProceed();

        // Nastavíme observer pro nové embedy
        if (globalConfig.observeNewEmbeds) {
            setupMutationObserver();
        }

        // Vytvoříme manuální tlačítko
        if (globalConfig.showManualButton) {
            createManualButton();
        }
    }

    /**
     * Vytvoří manuální tlačítko pro testování
     */
    function createManualButton() {
        // Odstraníme staré tlačítko pokud existuje
        if (manualButton) {
            manualButton.remove();
        }

        manualButton = document.createElement('div');
        manualButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
            user-select: none;
        `;

        manualButton.innerHTML = `
            <div>🎬 Instagram Video Ads</div>
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                Klikni pro detekci embedů
            </div>
        `;

        // Hover efekt
        manualButton.addEventListener('mouseenter', () => {
            manualButton.style.transform = 'scale(1.05)';
        });
        manualButton.addEventListener('mouseleave', () => {
            manualButton.style.transform = 'scale(1)';
        });

        // Kliknutí
        manualButton.addEventListener('click', () => {
            log('Manuální spuštění detekce embedů');
            manualButton.style.background = '#28a745';
            manualButton.innerHTML = `
                <div>🔄 Hledám embedy...</div>
                <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                    Retry: ${retryCount}/${globalConfig.maxRetries}
                </div>
            `;

            restartDetection();

            // Resetujeme tlačítko po chvíli
            setTimeout(() => {
                if (manualButton) {
                    manualButton.style.background = 'linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%)';
                    manualButton.innerHTML = `
                        <div>🎬 Instagram Video Ads</div>
                        <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                            Klikni pro detekci embedů
                        </div>
                    `;
                }
            }, 3000);
        });

        document.body.appendChild(manualButton);
        log('Manuální tlačítko vytvořeno');

        // Pravidelně aktualizujeme stav tlačítka
        setInterval(updateManualButtonStatus, 2000);
    }

    /**
     * Zkontroluje, zda je načten Instagram embed script
     */
    function checkInstagramScript() {
        // Hledáme Instagram embed script
        const instagramScripts = document.querySelectorAll('script[src*="instagram.com/embed"]');
        if (instagramScripts.length > 0) {
            log('Instagram embed script nalezen');
            instagramScriptLoaded = true;
            return;
        }

        // Hledáme také window.instgrm objekt
        if (window.instgrm && window.instgrm.Embeds) {
            log('Instagram Embeds API nalezeno');
            instagramScriptLoaded = true;
            return;
        }

        log('Instagram script zatím nenalezen, bude se kontrolovat později');
    }

    /**
     * Čeká na dokončení načítání Instagram embedů
     */
    function waitForInstagramEmbeds() {
        return new Promise((resolve) => {
            // Pokud máme instgrm API, použijeme ho
            if (window.instgrm && window.instgrm.Embeds && window.instgrm.Embeds.process) {
                log('Spouštím Instagram Embeds.process()');
                try {
                    window.instgrm.Embeds.process();
                } catch (e) {
                    log('Chyba při volání Instagram Embeds.process():', e);
                }
            }

            // Počkáme chvíli na zpracování
            setTimeout(resolve, 1000);
        });
    }

    /**
     * Načte Google IMA SDK
     */
    function loadImaScript() {
        if (window.google && window.google.ima) {
            log('IMA SDK už je načteno');
            imaLoaded = true;
            return;
        }

        log('Načítám IMA SDK...');
        const script = document.createElement('script');
        script.src = 'https://imasdk.googleapis.com/js/sdkloader/ima3.js';
        script.async = true;
        script.onload = () => {
            log('IMA SDK úspěšně načteno');
            imaLoaded = true;
        };
        script.onerror = () => {
            error('Chyba při načítání IMA SDK');
            imaLoaded = true;
        };
        document.head.appendChild(script);
    }

    /**
     * Zkontroluje IMA SDK a pokračuje
     */
    function checkImaAndProceed() {
        if (imaLoaded) {
            log('IMA SDK připraveno, spouštím robustní detekci embedů...');
            startRobustEmbedDetection();
        } else {
            log('Čekám na IMA SDK...');
            setTimeout(checkImaAndProceed, 200);
        }
    }

    /**
     * Spustí robustní detekci embedů s opakovanými pokusy
     */
    async function startRobustEmbedDetection() {
        log(`Pokus ${retryCount + 1}/${globalConfig.maxRetries} - hledám Instagram embedy`);

        // Zkontrolujeme Instagram script
        if (globalConfig.waitForInstagramScript && !instagramScriptLoaded) {
            checkInstagramScript();
        }

        // Počkáme na Instagram embedy
        if (instagramScriptLoaded || window.instgrm) {
            await waitForInstagramEmbeds();
        }

        // Zpracujeme embedy
        const processedCount = await processEmbedsWithRetry();

        if (processedCount > 0) {
            log(`Úspěšně zpracováno ${processedCount} embedů`);
            return;
        }

        // Pokud jsme nenašli žádné embedy a máme ještě pokusy
        retryCount++;
        if (retryCount < globalConfig.maxRetries) {
            log(`Žádné embedy nenalezeny, zkusím znovu za ${globalConfig.retryInterval}ms`);
            retryTimer = setTimeout(startRobustEmbedDetection, globalConfig.retryInterval);
        } else {
            log('Dosažen maximální počet pokusů, ukončuji detekci');
        }
    }

    /**
     * Nastaví MutationObserver pro detekci nových embedů
     */
    function setupMutationObserver() {
        if (!window.MutationObserver) {
            log('MutationObserver není podporován');
            return;
        }

        observer = new MutationObserver((mutations) => {
            let newEmbedsFound = false;
            mutations.forEach((mutation) => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) {
                            // Hledáme pouze specifické Instagram embedy
                            const specificSelectors = [
                                'blockquote.instagram-media',
                                'iframe[src*="instagram.com/p/"]',
                                'iframe[src*="instagram.com/embed"]',
                                '[data-instgrm-permalink]'
                            ];

                            let found = false;
                            specificSelectors.forEach(selector => {
                                const embeds = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                if (embeds.length > 0) {
                                    // Ověříme, že jsou to validní embedy
                                    Array.from(embeds).forEach(embed => {
                                        if (isValidInstagramEmbed(embed)) {
                                            found = true;
                                        }
                                    });
                                } else if (node.matches && node.matches(selector) && isValidInstagramEmbed(node)) {
                                    found = true;
                                }
                            });

                            if (found) {
                                newEmbedsFound = true;
                                log('Nové validní Instagram embedy detekované přes MutationObserver');
                            }

                            // Také zkontrolujeme Instagram script
                            if (node.tagName === 'SCRIPT' && node.src && node.src.includes('instagram.com/embed')) {
                                log('Instagram embed script detekován');
                                instagramScriptLoaded = true;
                            }
                        }
                    }
                }
            });

            if (newEmbedsFound && imaLoaded) {
                log('Zpracovávám nově detekované validní embedy s krátkým zpožděním');
                // Počkáme chvíli, než se embedy plně načtou
                setTimeout(() => {
                    processEmbedsWithRetry();
                }, 1000);
            }
        });

        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
        log('MutationObserver spuštěn');
    }

    /**
     * Zpracuje všechny Instagram embedy s retry logikou
     */
    async function processEmbedsWithRetry() {
        log('🔍 Spouštím selektivní hledání Instagram embedů...');

        // Hledáme pouze specifické Instagram embedy (ne obecné elementy)
        const specificSelectors = [
            'blockquote.instagram-media',
            'iframe[src*="instagram.com/p/"]',
            'iframe[src*="instagram.com/embed"]',
            '[data-instgrm-permalink]'
        ];

        let validEmbeds = [];

        // Projdeme pouze specifické selektory
        specificSelectors.forEach(selector => {
            try {
                const found = document.querySelectorAll(selector);
                if (found.length > 0) {
                    log(`📍 Selector "${selector}" našel ${found.length} elementů`);
                    Array.from(found).forEach(embed => {
                        if (isValidInstagramEmbed(embed)) {
                            validEmbeds.push(embed);
                        }
                    });
                }
            } catch (e) {
                log(`⚠️ Chyba při hledání "${selector}":`, e);
            }
        });

        log(`📊 Nalezeno ${validEmbeds.length} validních Instagram embedů`);

        // Filtrujeme už zpracované embedy
        const unprocessedEmbeds = validEmbeds.filter(embed => {
            return !players.some(player => player.originalEmbed === embed);
        });

        log(`🆕 Zpracovávám ${unprocessedEmbeds.length} nových embedů`);

        if (unprocessedEmbeds.length === 0) {
            log('❌ Žádné nové embedy k zpracování');
            return 0;
        }

        // Zpracujeme embedy s čekáním na jejich rozměry
        const processPromises = unprocessedEmbeds.map((embed, index) =>
            preparePlayerAsync(embed, players.length + index)
        );

        await Promise.all(processPromises);
        log(`✅ Úspěšně zpracováno ${unprocessedEmbeds.length} embedů`);
        return unprocessedEmbeds.length;
    }

    /**
     * Zkontroluje, zda je element validní Instagram embed
     */
    function isValidInstagramEmbed(embed) {
        const rect = embed.getBoundingClientRect();
        const tagName = embed.tagName.toLowerCase();

        // Základní kontroly rozměrů - Instagram embedy mají typicky 300-600px šířku a 400-1000px výšku
        const hasReasonableSize = rect.width >= 300 && rect.width <= 800 &&
                                 rect.height >= 400 && rect.height <= 1200;

        // Vyloučíme HTML, BODY a kontejnery
        const isNotPageElement = !['html', 'body', 'main', 'section', 'article'].includes(tagName);

        // Kontrola, že není celá stránka - upravená logika
        // Instagram embed může být vysoký, ale nesmí zabírat celou šířku stránky
        const isNotFullPage = rect.width < window.innerWidth * 0.95; // Pouze šířka je důležitá

        // Specifické kontroly pro Instagram
        let isInstagramSpecific = false;

        if (tagName === 'blockquote' && embed.classList.contains('instagram-media')) {
            isInstagramSpecific = true;
        } else if (tagName === 'iframe' && embed.src &&
                  (embed.src.includes('instagram.com/p/') || embed.src.includes('instagram.com/embed'))) {
            isInstagramSpecific = true;
        } else if (embed.hasAttribute('data-instgrm-permalink')) {
            isInstagramSpecific = true;
        } else if (embed.classList.contains('instagram-media')) {
            isInstagramSpecific = true;
        }

        // Dodatečná kontrola - nesmí to být wrapper nebo kontejner
        const isNotWrapper = !embed.classList.contains('container') &&
                            !embed.classList.contains('wrapper') &&
                            !embed.id.includes('container') &&
                            !embed.id.includes('wrapper');

        const isValid = hasReasonableSize && isNotPageElement && isNotFullPage && isInstagramSpecific && isNotWrapper;

        if (isValid) {
            log(`✅ Validní Instagram embed: ${tagName}.${embed.className} (${Math.round(rect.width)}x${Math.round(rect.height)}px)`);
        } else {
            log(`❌ Nevalidní embed: ${tagName}.${embed.className} (${Math.round(rect.width)}x${Math.round(rect.height)}px)`);
            log(`   - rozměry: ${hasReasonableSize} (${Math.round(rect.width)}x${Math.round(rect.height)})`);
            log(`   - není stránka: ${isNotPageElement} (${tagName})`);
            log(`   - není fullpage: ${isNotFullPage} (šířka ${Math.round(rect.width)} < ${Math.round(window.innerWidth * 0.95)})`);
            log(`   - je Instagram: ${isInstagramSpecific}`);
            log(`   - není wrapper: ${isNotWrapper}`);
        }

        return isValid;
    }

    /**
     * Zpracuje všechny Instagram embedy (původní funkce pro kompatibilitu)
     */
    function processEmbeds() {
        processEmbedsWithRetry().then(count => {
            log(`Zpracováno ${count} embedů`);
        });
    }

    /**
     * Připraví jednotlivý embed pro reklamu (asynchronní verze)
     */
    async function preparePlayerAsync(embed, index) {
        return new Promise((resolve) => {
            waitForEmbedDimensions(embed, () => {
                const rect = embed.getBoundingClientRect();
                const width = Math.round(rect.width) || 540;
                const height = Math.round(rect.height) || 600;

                log(`Připravuji player ${index} (${width}x${height}px)`);

                const playerState = {
                    id: `instagram-ad-player-${index}`,
                    originalEmbed: embed,
                    wrapper: null,
                    adContainer: null,
                    playButton: null,
                    overlay: null,
                    adsLoader: null,
                    adsManager: null,
                    adDisplayContainer: null,
                    isAdPlaying: false,
                    adRequested: false,
                    width: width,
                    height: height
                };

                players.push(playerState);
                createWrapper(playerState);
                resolve();
            });
        });
    }

    /**
     * Připraví jednotlivý embed pro reklamu (původní funkce pro kompatibilitu)
     */
    function preparePlayer(embed, index) {
        preparePlayerAsync(embed, index);
    }

    /**
     * Počká na ustálení rozměrů embedu
     */
    function waitForEmbedDimensions(embed, callback) {
        let attempts = 0;
        const maxAttempts = 30; // Zvýšeno z 20
        let lastWidth = 0;
        let lastHeight = 0;
        let stableCount = 0;

        function checkDimensions() {
            const rect = embed.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(embed);

            // Zkusíme získat rozměry různými způsoby
            const width = rect.width || embed.offsetWidth || parseInt(computedStyle.width) || 0;
            const height = rect.height || embed.offsetHeight || parseInt(computedStyle.height) || 0;

            log(`Pokus ${attempts + 1}: rozměry embedu ${width}x${height}px`);

            // Kontrolujeme, zda jsou rozměry stabilní
            if (width === lastWidth && height === lastHeight && width > 100 && height > 100) {
                stableCount++;
                if (stableCount >= 2) { // Rozměry musí být stabilní alespoň 2 pokusy
                    log(`Rozměry embedu stabilní: ${width}x${height}px`);
                    callback();
                    return;
                }
            } else {
                stableCount = 0;
            }

            lastWidth = width;
            lastHeight = height;
            attempts++;

            if (attempts < maxAttempts) {
                setTimeout(checkDimensions, 300); // Zvýšeno z 250ms
            } else {
                log('Timeout při čekání na rozměry embedu, pokračuji s posledními známými rozměry');
                callback();
            }
        }

        // Počkáme chvíli před prvním pokusem
        setTimeout(checkDimensions, 500);
    }

    /**
     * Vytvoří wrapper a overlay pro embed
     */
    function createWrapper(playerState) {
        const embed = playerState.originalEmbed;

        // Najdeme vhodný kontejner pro overlay - použijeme přímo iframe
        let targetElement = embed;
        let overlay = null;

        // Pro iframe použijeme rodiče jako target element
        if (embed.tagName.toLowerCase() === 'iframe') {
            // Získáme skutečné rozměry iframe
            const rect = embed.getBoundingClientRect();
            playerState.width = Math.round(rect.width);
            playerState.height = Math.round(rect.height);

            log(`Použiji iframe: ${embed.tagName}.${embed.className} (${playerState.width}x${playerState.height}px)`);

            // Použijeme rodiče iframe jako target element
            const parent = embed.parentElement;
            if (parent) {
                // Nastavíme position: relative pro rodiče
                if (window.getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                targetElement = parent;

                // Získáme pozici iframe relativně k rodiči
                const parentRect = parent.getBoundingClientRect();
                const relativeTop = rect.top - parentRect.top;
                const relativeLeft = rect.left - parentRect.left;

                // Vytvoříme overlay přesně v pozici a rozměrech iframe
                overlay = document.createElement('div');
                overlay.className = 'instagram-ad-overlay';
                overlay.setAttribute('data-player-id', playerState.id);
                overlay.style.cssText = `
                    position: absolute;
                    top: ${relativeTop}px;
                    left: ${relativeLeft}px;
                    width: ${playerState.width}px;
                    height: ${playerState.height}px;
                    background: rgba(0, 0, 0, 0.7);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                    cursor: pointer;
                    border-radius: 3px;
                    pointer-events: auto;
                    box-sizing: border-box;
                `;

                log(`Overlay pozice: top=${relativeTop}px, left=${relativeLeft}px`);
            } else {
                log('Chyba: iframe nemá rodiče!');
                return null;
            }
        } else {
            // Pro ostatní elementy nastavíme position: relative
            targetElement.style.position = 'relative';

            // Vytvoříme overlay standardně
            overlay = document.createElement('div');
            overlay.className = 'instagram-ad-overlay';
            overlay.setAttribute('data-player-id', playerState.id);
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: ${playerState.width}px;
                height: ${playerState.height}px;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                cursor: pointer;
                border-radius: 3px;
                pointer-events: auto;
                box-sizing: border-box;
            `;
        }

        // Pokud overlay nebyl vytvořen, ukončíme
        if (!overlay) {
            log('Chyba: overlay nebyl vytvořen!');
            return null;
        }

        // Vytvoříme Play tlačítko
        const playButton = document.createElement('div');
        playButton.style.cssText = `
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
            pointer-events: auto;
        `;

        // Play ikona
        playButton.innerHTML = `
            <div style="
                width: 0;
                height: 0;
                border-left: 20px solid white;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
                margin-left: 4px;
            "></div>
        `;

        // Hover efekt
        playButton.addEventListener('mouseenter', () => {
            playButton.style.transform = 'scale(1.1)';
        });
        playButton.addEventListener('mouseleave', () => {
            playButton.style.transform = 'scale(1)';
        });

        overlay.appendChild(playButton);

        // Kliknutí na overlay
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            log(`Play kliknuto pro player ${playerState.id}`);

            if (!playerState.adRequested) {
                playerState.adRequested = true;
                requestAd(playerState);
            }
        }, { once: true });

        // Přidáme overlay do target elementu
        targetElement.appendChild(overlay);

        playerState.overlay = overlay;
        playerState.playButton = playButton;
        playerState.targetElement = targetElement;

        log(`Overlay vytvořen pro player ${playerState.id} v elementu ${targetElement.tagName}.${targetElement.className}`);

        // Debug: zkontrolujeme, zda je overlay skutečně v DOM
        setTimeout(() => {
            const foundOverlay = document.querySelector(`[data-player-id="${playerState.id}"]`);
            if (foundOverlay) {
                log(`✅ Overlay ${playerState.id} úspěšně nalezen v DOM`);
            } else {
                log(`❌ Overlay ${playerState.id} NENALEZEN v DOM!`);
            }
        }, 100);
    }

    /**
     * Požádá o reklamu
     */
    function requestAd(playerState) {
        log(`Požaduji reklamu pro player ${playerState.id}`);

        // Skryjeme overlay
        if (playerState.overlay) {
            playerState.overlay.style.display = 'none';
        }

        // Zkontrolujeme IMA SDK
        if (!window.google || !window.google.ima) {
            error('IMA SDK není dostupné');
            return;
        }

        try {
            // Získáme pozici iframe relativně k jeho rodiči
            const iframe = playerState.originalEmbed;
            const parent = iframe.parentElement;
            const iframeRect = iframe.getBoundingClientRect();
            const parentRect = parent.getBoundingClientRect();

            const relativeTop = iframeRect.top - parentRect.top;
            const relativeLeft = iframeRect.left - parentRect.left;

            // Vytvoříme ad kontejner přesně v pozici a rozměrech iframe
            const adContainer = document.createElement('div');
            adContainer.style.cssText = `
                position: absolute;
                top: ${relativeTop}px;
                left: ${relativeLeft}px;
                width: ${playerState.width}px;
                height: ${playerState.height}px;
                background: #000;
                z-index: 10001;
                border-radius: 3px;
                box-sizing: border-box;
            `;

            log(`Ad kontejner pozice: top=${relativeTop}px, left=${relativeLeft}px, size=${playerState.width}x${playerState.height}px`);

            // Vytvoříme video element
            const videoElement = document.createElement('video');
            videoElement.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: contain;
                display: block;
            `;
            videoElement.setAttribute('playsinline', '');
            videoElement.setAttribute('controls', 'false');

            adContainer.appendChild(videoElement);

            // Přidáme ad kontejner k rodiči iframe (iframe nemůže obsahovat další elementy)
            const parentElement = playerState.originalEmbed.parentElement;
            if (parentElement) {
                // Nastavíme position: relative pro rodiče
                if (window.getComputedStyle(parentElement).position === 'static') {
                    parentElement.style.position = 'relative';
                }
                parentElement.appendChild(adContainer);
                log(`Ad kontejner přidán k rodiči iframe: ${parentElement.tagName}.${parentElement.className}`);
            } else {
                // Fallback - přidáme k body
                document.body.appendChild(adContainer);
                log(`Ad kontejner přidán k body (fallback)`);
            }

            playerState.adContainer = adContainer;

            // Inicializujeme IMA
            playerState.adDisplayContainer = new google.ima.AdDisplayContainer(adContainer, videoElement);
            playerState.adDisplayContainer.initialize();

            playerState.adsLoader = new google.ima.AdsLoader(playerState.adDisplayContainer);

            // Event listenery
            playerState.adsLoader.addEventListener(
                google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
                (event) => onAdsManagerLoaded(event, playerState),
                false
            );

            playerState.adsLoader.addEventListener(
                google.ima.AdErrorEvent.Type.AD_ERROR,
                (event) => onAdError(event, playerState),
                false
            );

            // Vytvoříme požadavek na reklamu
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = globalConfig.adTagUrl + Date.now();
            adsRequest.linearAdSlotWidth = playerState.width;
            adsRequest.linearAdSlotHeight = playerState.height;

            log(`Požaduji reklamu s URL: ${adsRequest.adTagUrl}`);
            playerState.adsLoader.requestAds(adsRequest);

        } catch (e) {
            error('Chyba při požadavku na reklamu:', e);
            cleanupAd(playerState);
        }
    }

    /**
     * Handler pro načtení AdsManageru
     */
    function onAdsManagerLoaded(adsManagerLoadedEvent, playerState) {
        log(`AdsManager načten pro ${playerState.id}`);

        const adsRenderingSettings = new google.ima.AdsRenderingSettings();
        adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete = true;

        playerState.adsManager = adsManagerLoadedEvent.getAdsManager(
            playerState.adContainer.querySelector('video'),
            adsRenderingSettings
        );

        // Event listenery pro AdsManager
        playerState.adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (event) => onAdError(event, playerState));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED, () => onContentResumeRequested(playerState));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED, () => onAllAdsCompleted(playerState));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.LOADED, () => log(`Reklama načtena pro ${playerState.id}`));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.STARTED, () => log(`Reklama spuštěna pro ${playerState.id}`));

        try {
            playerState.adsManager.init(playerState.width, playerState.height, google.ima.ViewMode.NORMAL);
            playerState.adsManager.start();
            log(`AdsManager spuštěn pro ${playerState.id}`);
        } catch (adError) {
            error(`Chyba při spuštění AdsManageru pro ${playerState.id}:`, adError);
            cleanupAd(playerState);
        }
    }

    /**
     * Handler pro chyby reklamy
     */
    function onAdError(adErrorEvent, playerState) {
        error(`Chyba reklamy pro ${playerState.id}:`, adErrorEvent.getError());
        cleanupAd(playerState);
    }

    /**
     * Handler pro dokončení všech reklam
     */
    function onAllAdsCompleted(playerState) {
        log(`Všechny reklamy dokončeny pro ${playerState.id}`);
        cleanupAd(playerState);
    }

    /**
     * Handler pro požadavek na obnovení obsahu
     */
    function onContentResumeRequested(playerState) {
        log(`Požadavek na obnovení obsahu pro ${playerState.id}`);
        cleanupAd(playerState);
    }

    /**
     * Vyčistí reklamu a odstraní vše
     */
    function cleanupAd(playerState) {
        log(`Ukončuji reklamu pro ${playerState.id}`);

        // Zničíme AdsManager
        if (playerState.adsManager) {
            playerState.adsManager.destroy();
            playerState.adsManager = null;
        }

        // Odstraníme ad kontejner
        if (playerState.adContainer) {
            playerState.adContainer.remove();
            playerState.adContainer = null;
        }

        // Odstraníme overlay úplně
        if (playerState.overlay) {
            playerState.overlay.remove();
            playerState.overlay = null;
        }

        // Také zkusíme najít overlay podle data-player-id
        const overlayById = document.querySelector(`[data-player-id="${playerState.id}"]`);
        if (overlayById) {
            overlayById.remove();
            log(`Overlay ${playerState.id} odstraněn podle ID`);
        }

        log(`Vše vyčištěno pro ${playerState.id} - uživatel může prohlížet původní obsah`);
    }

    /**
     * Vyčistí retry timer
     */
    function clearRetryTimer() {
        if (retryTimer) {
            clearTimeout(retryTimer);
            retryTimer = null;
        }
    }

    /**
     * Restartuje detekci embedů
     */
    function restartDetection() {
        log('Restartování detekce embedů...');
        clearRetryTimer();
        retryCount = 0;
        updateManualButtonStatus();
        if (imaLoaded) {
            startRobustEmbedDetection();
        } else {
            checkImaAndProceed();
        }
    }

    /**
     * Aktualizuje stav manuálního tlačítka
     */
    function updateManualButtonStatus() {
        if (!manualButton) return;

        // Spočítáme validní embedy
        const allPotentialEmbeds = document.querySelectorAll('blockquote.instagram-media, iframe[src*="instagram.com/p/"], iframe[src*="instagram.com/embed"], [data-instgrm-permalink]');
        let validEmbedsCount = 0;

        Array.from(allPotentialEmbeds).forEach(embed => {
            if (isValidInstagramEmbed(embed)) {
                validEmbedsCount++;
            }
        });

        const playersCount = players.length;

        manualButton.innerHTML = `
            <div>🎬 Instagram Video Ads</div>
            <div style="font-size: 11px; margin-top: 3px; opacity: 0.9;">
                Validní: ${validEmbedsCount} | Players: ${playersCount}
            </div>
            <div style="font-size: 11px; opacity: 0.9;">
                Retry: ${retryCount}/${globalConfig.maxRetries}
            </div>
        `;
    }

    // Expose API
    window.InstagramVideoAds = {
        init: init,
        processEmbeds: processEmbeds,
        processEmbedsWithRetry: processEmbedsWithRetry,
        restartDetection: restartDetection,
        clearRetryTimer: clearRetryTimer,
        config: (newConfig) => {
            globalConfig = { ...globalConfig, ...newConfig };
            log('Konfigurace aktualizována:', globalConfig);
            return globalConfig;
        },
        getPlayers: () => players,
        getRetryCount: () => retryCount,
        isInstagramScriptLoaded: () => instagramScriptLoaded,
        updateButtonStatus: updateManualButtonStatus,
        debugInfo: () => {
            const allPotentialEmbeds = document.querySelectorAll('blockquote.instagram-media, iframe[src*="instagram.com/p/"], iframe[src*="instagram.com/embed"], [data-instgrm-permalink]');
            let validEmbedsCount = 0;
            const embedDetails = [];

            Array.from(allPotentialEmbeds).forEach(embed => {
                const rect = embed.getBoundingClientRect();
                const isValid = isValidInstagramEmbed(embed);
                if (isValid) {
                    validEmbedsCount++;
                }

                embedDetails.push({
                    tagName: embed.tagName,
                    className: embed.className,
                    id: embed.id,
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    src: embed.src || 'N/A',
                    isValid: isValid
                });
            });

            return {
                potentialEmbeds: allPotentialEmbeds.length,
                validEmbeds: validEmbedsCount,
                playersCreated: players.length,
                retryCount: retryCount,
                maxRetries: globalConfig.maxRetries,
                imaLoaded: imaLoaded,
                instagramScriptLoaded: instagramScriptLoaded,
                windowSize: `${window.innerWidth}x${window.innerHeight}`,
                embedDetails: embedDetails,
                config: globalConfig,
                players: players.map(p => ({
                    id: p.id,
                    width: p.width,
                    height: p.height,
                    tagName: p.originalEmbed.tagName,
                    className: p.originalEmbed.className
                }))
            };
        },
        forceProcessEmbed: (selector) => {
            // Funkce pro ruční zpracování embedu
            const embed = document.querySelector(selector);
            if (embed) {
                log(`🔧 Ruční zpracování embedu: ${selector}`);
                preparePlayerAsync(embed, players.length);
                return true;
            }
            return false;
        },
        findOverlays: () => {
            // Najde všechny overlay elementy
            const overlays = document.querySelectorAll('.instagram-ad-overlay');
            const overlaysById = document.querySelectorAll('[data-player-id]');

            return {
                byClass: overlays.length,
                byDataId: overlaysById.length,
                overlays: Array.from(overlaysById).map(o => ({
                    playerId: o.getAttribute('data-player-id'),
                    visible: o.offsetWidth > 0 && o.offsetHeight > 0,
                    zIndex: window.getComputedStyle(o).zIndex,
                    position: window.getComputedStyle(o).position,
                    display: window.getComputedStyle(o).display
                }))
            };
        },
        showTestOverlay: () => {
            // Vytvoří testovací overlay pro debug
            const testOverlay = document.createElement('div');
            testOverlay.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 200px;
                height: 200px;
                background: rgba(255, 0, 0, 0.8);
                z-index: 99999;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 20px;
                cursor: pointer;
            `;
            testOverlay.textContent = 'TEST OVERLAY';
            testOverlay.onclick = () => testOverlay.remove();
            document.body.appendChild(testOverlay);

            setTimeout(() => {
                if (testOverlay.parentNode) {
                    testOverlay.remove();
                }
            }, 5000);

            return 'Test overlay vytvořen na 5 sekund';
        }
    };

    // Auto-inicializace
    if (defaultConfig.autoInit) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => init(), defaultConfig.initDelay);
            });
        } else {
            setTimeout(() => init(), defaultConfig.initDelay);
        }
    }

})(window, document);
