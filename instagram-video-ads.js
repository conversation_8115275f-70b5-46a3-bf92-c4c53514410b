/**
 * Instagram Video Ads Module
 *
 * Automaticky detekuje Instagram embedy a vkládá video reklamy
 * Inspirováno funkčním YouTube detektorem
 */

(function(window, document) {
    'use strict';

    // Konfigurace - optimalizováno pro produkci
    const defaultConfig = {
        adTagUrl: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&impl=s&correlator=',
        debug: false,
        autoInit: true,
        initDelay: 2000,
        retryInterval: 1500,
        maxRetries: 8,
        observeNewEmbeds: true,
        waitForInstagramScript: true,
        enablePreloading: true
    };

    // Konstanty pro selektory a styly - rozš<PERSON><PERSON>eno pro produkci
    const INSTAGRAM_SELECTORS = [
        'blockquote.instagram-media',
        'iframe[src*="instagram.com/p/"]',
        'iframe[src*="instagram.com/embed"]',
        '[data-instgrm-permalink]',
        '.instagram-media',
        'blockquote[class*="instagram"]',
        'iframe[src*="instagram.com"]'
    ];

    const OVERLAY_STYLES = `
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        cursor: pointer;
        border-radius: 3px;
        pointer-events: auto;
        box-sizing: border-box;
    `;

    const PLAY_BUTTON_STYLES = `
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;
        pointer-events: auto;
    `;

    const VOLUME_CONTROL_STYLES = `
        position: absolute;
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        font-size: 18px;
        transition: background 0.3s ease;
        z-index: 10002;
        pointer-events: auto;
    `;

    let globalConfig = {};
    let imaLoaded = false;
    const players = [];
    let observer = null;
    let retryCount = 0;
    let retryTimer = null;
    let instagramScriptLoaded = false;

    function log(...args) {
        if (globalConfig.debug) {
            console.log('[Instagram Video Ads]', ...args);
        }
    }

    function error(...args) {
        console.error('[Instagram Video Ads]', ...args);
    }

    /**
     * Inicializace modulu
     */
    function init(userConfig = {}) {
        globalConfig = { ...defaultConfig, ...userConfig };
        log('Inicializace Instagram Video Ads');

        // Resetujeme retry počítadlo
        retryCount = 0;

        // Načteme IMA SDK
        loadImaScript();

        // Zkontrolujeme Instagram script
        if (globalConfig.waitForInstagramScript) {
            checkInstagramScript();
        }

        // Počkáme na IMA SDK a pak zpracujeme embedy
        checkImaAndProceed();

        // Nastavíme observer pro nové embedy
        if (globalConfig.observeNewEmbeds) {
            setupMutationObserver();
        }
    }

    /**
     * Zkontroluje, zda je načten Instagram embed script
     */
    function checkInstagramScript() {
        // Hledáme Instagram embed script
        const instagramScripts = document.querySelectorAll('script[src*="instagram.com/embed"]');
        if (instagramScripts.length > 0) {
            log('Instagram embed script nalezen');
            instagramScriptLoaded = true;
            return;
        }

        // Hledáme také window.instgrm objekt
        if (window.instgrm && window.instgrm.Embeds) {
            log('Instagram Embeds API nalezeno');
            instagramScriptLoaded = true;
            return;
        }

        log('Instagram script zatím nenalezen, bude se kontrolovat později');
    }

    /**
     * Čeká na dokončení načítání Instagram embedů
     */
    function waitForInstagramEmbeds() {
        return new Promise((resolve) => {
            // Pokud máme instgrm API, použijeme ho
            if (window.instgrm && window.instgrm.Embeds && window.instgrm.Embeds.process) {
                log('Spouštím Instagram Embeds.process()');
                try {
                    window.instgrm.Embeds.process();
                } catch (e) {
                    log('Chyba při volání Instagram Embeds.process():', e);
                }
            }

            // Počkáme kratší dobu na zpracování
            setTimeout(resolve, 500); // Zkráceno z 1000ms na 500ms
        });
    }

    /**
     * Načte Google IMA SDK
     */
    function loadImaScript() {
        if (window.google && window.google.ima) {
            log('IMA SDK už je načteno');
            imaLoaded = true;
            return;
        }

        log('Načítám IMA SDK...');
        const script = document.createElement('script');
        script.src = 'https://imasdk.googleapis.com/js/sdkloader/ima3.js';
        script.async = true;
        script.onload = () => {
            log('IMA SDK úspěšně načteno');
            imaLoaded = true;
        };
        script.onerror = () => {
            error('Chyba při načítání IMA SDK');
            imaLoaded = true;
        };
        document.head.appendChild(script);
    }

    /**
     * Zkontroluje IMA SDK a pokračuje
     */
    function checkImaAndProceed() {
        if (imaLoaded) {
            log('IMA SDK připraveno, spouštím robustní detekci embedů...');
            startRobustEmbedDetection();
        } else {
            log('Čekám na IMA SDK...');
            setTimeout(checkImaAndProceed, 100); // Zkráceno z 200ms na 100ms
        }
    }

    /**
     * Spustí robustní detekci embedů s opakovanými pokusy
     */
    async function startRobustEmbedDetection() {
        log(`Pokus ${retryCount + 1}/${globalConfig.maxRetries} - hledám Instagram embedy`);

        // Zkontrolujeme Instagram script
        if (globalConfig.waitForInstagramScript && !instagramScriptLoaded) {
            checkInstagramScript();
        }

        // Počkáme na Instagram embedy
        if (instagramScriptLoaded || window.instgrm) {
            await waitForInstagramEmbeds();
        }

        // Zpracujeme embedy
        const processedCount = await processEmbedsWithRetry();

        if (processedCount > 0) {
            log(`Úspěšně zpracováno ${processedCount} embedů`);
            return;
        }

        // Pokud jsme nenašli žádné embedy a máme ještě pokusy
        retryCount++;
        if (retryCount < globalConfig.maxRetries) {
            log(`Žádné embedy nenalezeny, zkusím znovu za ${globalConfig.retryInterval}ms`);
            retryTimer = setTimeout(startRobustEmbedDetection, globalConfig.retryInterval);
        } else {
            log('Dosažen maximální počet pokusů, ukončuji detekci');
        }
    }

    /**
     * Nastaví MutationObserver pro detekci nových embedů
     */
    function setupMutationObserver() {
        if (!window.MutationObserver) {
            log('MutationObserver není podporován');
            return;
        }

        observer = new MutationObserver((mutations) => {
            let newEmbedsFound = false;
            mutations.forEach((mutation) => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.nodeType === 1) {
                            // Hledáme pouze specifické Instagram embedy
                            let found = false;
                            INSTAGRAM_SELECTORS.forEach(selector => {
                                const embeds = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                if (embeds.length > 0) {
                                    // Ověříme, že jsou to validní embedy
                                    Array.from(embeds).forEach(embed => {
                                        if (isValidInstagramEmbed(embed)) {
                                            found = true;
                                        }
                                    });
                                } else if (node.matches && node.matches(selector) && isValidInstagramEmbed(node)) {
                                    found = true;
                                }
                            });

                            if (found) {
                                newEmbedsFound = true;
                                log('Nové validní Instagram embedy detekované přes MutationObserver');
                            }

                            // Také zkontrolujeme Instagram script
                            if (node.tagName === 'SCRIPT' && node.src && node.src.includes('instagram.com/embed')) {
                                log('Instagram embed script detekován');
                                instagramScriptLoaded = true;
                            }
                        }
                    }
                }
            });

            if (newEmbedsFound && imaLoaded) {
                log('Zpracovávám nově detekované validní embedy s krátkým zpožděním');
                // Počkáme kratší dobu, než se embedy plně načtou
                setTimeout(() => {
                    processEmbedsWithRetry();
                }, 500); // Zkráceno z 1000ms na 500ms
            }
        });

        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
        log('MutationObserver spuštěn');
    }

    /**
     * Zpracuje všechny Instagram embedy s retry logikou
     */
    async function processEmbedsWithRetry() {


        // Hledáme pouze specifické Instagram embedy (ne obecné elementy)
        let validEmbeds = [];

        // Projdeme pouze specifické selektory
        INSTAGRAM_SELECTORS.forEach(selector => {
            try {
                const found = document.querySelectorAll(selector);
                if (found.length > 0) {

                    Array.from(found).forEach(embed => {
                        if (isValidInstagramEmbed(embed)) {
                            validEmbeds.push(embed);
                        }
                    });
                }
            } catch (e) {
                error(`Chyba při hledání "${selector}":`, e);
            }
        });

        // Filtrujeme už zpracované embedy
        const unprocessedEmbeds = validEmbeds.filter(embed => {
            return !players.some(player => player.originalEmbed === embed);
        });

        if (unprocessedEmbeds.length === 0) {
            return 0;
        }

        // Zpracujeme embedy s čekáním na jejich rozměry
        const processPromises = unprocessedEmbeds.map((embed, index) =>
            preparePlayerAsync(embed, players.length + index)
        );

        await Promise.all(processPromises);
        return unprocessedEmbeds.length;
    }

    /**
     * Zkontroluje, zda je element validní Instagram embed
     */
    function isValidInstagramEmbed(embed) {
        const rect = embed.getBoundingClientRect();
        const tagName = embed.tagName.toLowerCase();

        // Základní kontroly rozměrů - Instagram embedy mají typicky 300-600px šířku a 400-1000px výšku
        const hasReasonableSize = rect.width >= 300 && rect.width <= 800 &&
                                 rect.height >= 400 && rect.height <= 1200;

        // Vyloučíme HTML, BODY a kontejnery
        const isNotPageElement = !['html', 'body', 'main', 'section', 'article'].includes(tagName);

        // Kontrola, že není celá stránka - upravená logika
        // Instagram embed může být vysoký, ale nesmí zabírat celou šířku stránky
        const isNotFullPage = rect.width < window.innerWidth * 0.95; // Pouze šířka je důležitá

        // Specifické kontroly pro Instagram
        let isInstagramSpecific = false;

        if (tagName === 'blockquote' && embed.classList.contains('instagram-media')) {
            isInstagramSpecific = true;
        } else if (tagName === 'iframe' && embed.src &&
                  (embed.src.includes('instagram.com/p/') || embed.src.includes('instagram.com/embed'))) {
            isInstagramSpecific = true;
        } else if (embed.hasAttribute('data-instgrm-permalink')) {
            isInstagramSpecific = true;
        } else if (embed.classList.contains('instagram-media')) {
            isInstagramSpecific = true;
        }

        // Dodatečná kontrola - nesmí to být wrapper nebo kontejner
        const isNotWrapper = !embed.classList.contains('container') &&
                            !embed.classList.contains('wrapper') &&
                            !embed.id.includes('container') &&
                            !embed.id.includes('wrapper');

        return hasReasonableSize && isNotPageElement && isNotFullPage && isInstagramSpecific && isNotWrapper;
    }

    /**
     * Zpracuje všechny Instagram embedy (původní funkce pro kompatibilitu)
     */
    function processEmbeds() {
        processEmbedsWithRetry().then(count => {
            log(`Zpracováno ${count} embedů`);
        });
    }

    /**
     * Připraví jednotlivý embed pro reklamu (asynchronní verze)
     */
    async function preparePlayerAsync(embed, index) {
        return new Promise((resolve) => {
            waitForEmbedDimensions(embed, () => {
                const rect = embed.getBoundingClientRect();
                const width = Math.round(rect.width) || 540;
                const height = Math.round(rect.height) || 600;



                const playerState = {
                    id: `instagram-ad-player-${index}`,
                    originalEmbed: embed,
                    wrapper: null,
                    adContainer: null,
                    playButton: null,
                    overlay: null,
                    adsLoader: null,
                    adsManager: null,
                    adDisplayContainer: null,
                    isAdPlaying: false,
                    adRequested: false,
                    width: width,
                    height: height,
                    hasValidAd: false,  // Nová vlastnost pro detekci dostupnosti reklamy
                    isMuted: false      // Nová vlastnost pro stav ztlumení
                };

                players.push(playerState);
                createWrapper(playerState);
                resolve();
            });
        });
    }

    /**
     * Počká na ustálení rozměrů embedu
     */
    function waitForEmbedDimensions(embed, callback) {
        let attempts = 0;
        const maxAttempts = 15; // Zkráceno z 30 na 15
        let lastWidth = 0;
        let lastHeight = 0;
        let stableCount = 0;

        function checkDimensions() {
            const rect = embed.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(embed);

            // Zkusíme získat rozměry různými způsoby
            const width = rect.width || embed.offsetWidth || parseInt(computedStyle.width) || 0;
            const height = rect.height || embed.offsetHeight || parseInt(computedStyle.height) || 0;

            // Kontrolujeme, zda jsou rozměry stabilní
            if (width === lastWidth && height === lastHeight && width > 100 && height > 100) {
                stableCount++;
                if (stableCount >= 1) { // Zkráceno z 2 na 1 pokus

                    callback();
                    return;
                }
            } else {
                stableCount = 0;
            }

            lastWidth = width;
            lastHeight = height;
            attempts++;

            if (attempts < maxAttempts) {
                setTimeout(checkDimensions, 200); // Zkráceno z 300ms na 200ms
            } else {

                callback();
            }
        }

        // Počkáme kratší dobu před prvním pokusem
        setTimeout(checkDimensions, 300); // Zkráceno z 500ms na 300ms
    }

    /**
     * Vytvoří wrapper a overlay pro embed
     */
    function createWrapper(playerState) {
        const embed = playerState.originalEmbed;

        // Najdeme vhodný kontejner pro overlay - použijeme přímo iframe
        let targetElement = embed;
        let overlay = null;

        // Pro iframe použijeme rodiče jako target element
        if (embed.tagName.toLowerCase() === 'iframe') {
            // Získáme skutečné rozměry iframe
            const rect = embed.getBoundingClientRect();
            playerState.width = Math.round(rect.width);
            playerState.height = Math.round(rect.height);



            // Použijeme rodiče iframe jako target element
            const parent = embed.parentElement;
            if (parent) {
                // Nastavíme position: relative pro rodiče
                if (window.getComputedStyle(parent).position === 'static') {
                    parent.style.position = 'relative';
                }
                targetElement = parent;

                // Získáme pozici iframe relativně k rodiči
                const parentRect = parent.getBoundingClientRect();
                const relativeTop = rect.top - parentRect.top;
                const relativeLeft = rect.left - parentRect.left;

                // Vytvoříme overlay přesně v pozici a rozměrech iframe
                overlay = document.createElement('div');
                overlay.className = 'instagram-ad-overlay';
                overlay.setAttribute('data-player-id', playerState.id);
                overlay.style.cssText = `
                    position: absolute;
                    top: ${relativeTop}px;
                    left: ${relativeLeft}px;
                    width: ${playerState.width}px;
                    height: ${playerState.height}px;
                    ${OVERLAY_STYLES}
                `;


            } else {
                return null;
            }
        } else {
            // Pro ostatní elementy nastavíme position: relative
            targetElement.style.position = 'relative';

            // Vytvoříme overlay standardně
            overlay = document.createElement('div');
            overlay.className = 'instagram-ad-overlay';
            overlay.setAttribute('data-player-id', playerState.id);
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: ${playerState.width}px;
                height: ${playerState.height}px;
                ${OVERLAY_STYLES}
            `;
        }

        // Pokud overlay nebyl vytvořen, ukončíme
        if (!overlay) {
            return null;
        }

        // Vytvoříme Play tlačítko
        const playButton = document.createElement('div');
        playButton.style.cssText = PLAY_BUTTON_STYLES;

        // Play ikona
        playButton.innerHTML = `
            <div style="
                width: 0;
                height: 0;
                border-left: 20px solid white;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
                margin-left: 4px;
            "></div>
        `;

        // Hover efekt
        playButton.addEventListener('mouseenter', () => {
            playButton.style.transform = 'scale(1.1)';
        });
        playButton.addEventListener('mouseleave', () => {
            playButton.style.transform = 'scale(1)';
        });

        overlay.appendChild(playButton);

        // Kliknutí na overlay - KRITICKÉ pro Chrome autoplay policy
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (!playerState.adRequested) {
                playerState.adRequested = true;

                // DŮLEŽITÉ: Inicializujeme AdDisplayContainer při user interaction
                // Toto je klíčové pro Chrome desktop podle Google IMA dokumentace
                if (playerState.adDisplayContainer) {
                    try {
                        playerState.adDisplayContainer.initialize();
                    } catch (initError) {
                        error('Chyba při inicializaci AdDisplayContainer:', initError);
                    }
                }

                requestAd(playerState);
            }
        }, { once: true });

        // Přidáme overlay do target elementu
        targetElement.appendChild(overlay);

        playerState.overlay = overlay;
        playerState.playButton = playButton;
        playerState.targetElement = targetElement;

        // Připravíme ad kontejner a AdDisplayContainer už teď (ale neinicializujeme)
        prepareAdContainer(playerState);


    }

    /**
     * Připraví ad kontejner a AdDisplayContainer (ale neinicializuje)
     */
    function prepareAdContainer(playerState) {
        try {
            // Získáme pozici iframe relativně k jeho rodiči
            const iframe = playerState.originalEmbed;
            const parent = iframe.parentElement;
            const iframeRect = iframe.getBoundingClientRect();
            const parentRect = parent.getBoundingClientRect();

            const relativeTop = iframeRect.top - parentRect.top;
            const relativeLeft = iframeRect.left - parentRect.left;

            // Vytvoříme ad kontejner přesně v pozici a rozměrech iframe
            const adContainer = document.createElement('div');
            adContainer.style.cssText = `
                position: absolute;
                top: ${relativeTop}px;
                left: ${relativeLeft}px;
                width: ${playerState.width}px;
                height: ${playerState.height}px;
                background: #000;
                z-index: 10001;
                border-radius: 3px;
                box-sizing: border-box;
                display: none;
            `;

            // Vytvoříme video element podle Google IMA best practices
            const videoElement = document.createElement('video');
            videoElement.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: contain;
                display: block;
                background: #000;
            `;

            // Atributy podle Google IMA dokumentace
            videoElement.setAttribute('playsinline', '');
            videoElement.setAttribute('webkit-playsinline', '');
            videoElement.setAttribute('controls', 'false');
            videoElement.setAttribute('preload', 'none');

            // Pro Chrome autoplay policy - začneme ztlumeni
            videoElement.muted = true;

            adContainer.appendChild(videoElement);

            // Přidáme ad kontejner k rodiči iframe
            const parentElement = playerState.originalEmbed.parentElement;
            if (parentElement) {
                // Nastavíme position: relative pro rodiče
                if (window.getComputedStyle(parentElement).position === 'static') {
                    parentElement.style.position = 'relative';
                }
                parentElement.appendChild(adContainer);
            } else {
                // Fallback - přidáme k body
                document.body.appendChild(adContainer);
            }

            playerState.adContainer = adContainer;
            playerState.videoElement = videoElement;

            // Vytvoříme AdDisplayContainer ale NEINICIALIZUJEME ho ještě
            // Inicializace musí být až při user interaction (click)
            playerState.adDisplayContainer = new google.ima.AdDisplayContainer(adContainer, videoElement);

            // Vytvoříme AdsLoader
            playerState.adsLoader = new google.ima.AdsLoader(playerState.adDisplayContainer);

            // Event listenery
            playerState.adsLoader.addEventListener(
                google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
                (event) => onAdsManagerLoaded(event, playerState),
                false
            );

            playerState.adsLoader.addEventListener(
                google.ima.AdErrorEvent.Type.AD_ERROR,
                (event) => onAdError(event, playerState),
                false
            );

        } catch (e) {
            error('Chyba při přípravě ad kontejneru:', e);
        }
    }

    /**
     * Vytvoří ovládací prvky pro zvuk
     */
    function createVolumeControls(playerState) {
        if (!playerState.adContainer || !playerState.adsManager) {
            return;
        }

        // Vytvoříme tlačítko pro ztlumení/zrušení ztlumení
        const volumeButton = document.createElement('div');
        volumeButton.className = 'instagram-ad-volume-control';
        volumeButton.style.cssText = VOLUME_CONTROL_STYLES;

        // Funkce pro aktualizaci ikony tlačítka
        function updateVolumeIcon() {
            const currentVolume = playerState.adsManager.getVolume();
            const isMuted = currentVolume === 0;

            volumeButton.innerHTML = isMuted ? '🔇' : '🔊';
            volumeButton.title = isMuted ? 'Zapnout zvuk' : 'Ztlumit';
            playerState.isMuted = isMuted;
        }

        // Kliknutí na tlačítko zvuku
        volumeButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (playerState.adsManager) {
                const currentVolume = playerState.adsManager.getVolume();
                const newVolume = currentVolume > 0 ? 0 : 1;

                playerState.adsManager.setVolume(newVolume);
                updateVolumeIcon();


            }
        });

        // Hover efekt
        volumeButton.addEventListener('mouseenter', () => {
            volumeButton.style.background = 'rgba(0, 0, 0, 0.9)';
        });
        volumeButton.addEventListener('mouseleave', () => {
            volumeButton.style.background = 'rgba(0, 0, 0, 0.7)';
        });

        // Přidáme tlačítko do ad kontejneru
        playerState.adContainer.appendChild(volumeButton);
        playerState.volumeButton = volumeButton;

        // Nastavíme počáteční ikonu
        updateVolumeIcon();


    }

    /**
     * Požádá o reklamu - používá už připravený ad kontejner
     */
    function requestAd(playerState) {
        // Skryjeme overlay
        if (playerState.overlay) {
            playerState.overlay.style.display = 'none';
        }

        // Zobrazíme ad kontejner
        if (playerState.adContainer) {
            playerState.adContainer.style.display = 'block';
        }

        // Zkontrolujeme IMA SDK
        if (!window.google || !window.google.ima) {
            error('IMA SDK není dostupné');
            return;
        }

        // Zkontrolujeme, zda máme připravený AdsLoader
        if (!playerState.adsLoader) {
            error('AdsLoader není připraven');
            return;
        }

        try {
            // Vytvoříme požadavek na reklamu
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = globalConfig.adTagUrl + Date.now();
            adsRequest.linearAdSlotWidth = playerState.width;
            adsRequest.linearAdSlotHeight = playerState.height;

            // Požádáme o reklamy
            playerState.adsLoader.requestAds(adsRequest);

        } catch (e) {
            error('Chyba při požadavku na reklamu:', e);
            cleanupAd(playerState);
        }
    }

    /**
     * Handler pro načtení AdsManageru
     */
    function onAdsManagerLoaded(adsManagerLoadedEvent, playerState) {
        const adsRenderingSettings = new google.ima.AdsRenderingSettings();
        adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete = true;

        // Povolíme preloading pro rychlejší načítání
        if (globalConfig.enablePreloading) {
            adsRenderingSettings.enablePreloading = true;
        }

        playerState.adsManager = adsManagerLoadedEvent.getAdsManager(
            playerState.videoElement,  // Používáme připravený video element
            adsRenderingSettings
        );

        // Event listenery pro AdsManager
        playerState.adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (event) => onAdError(event, playerState));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED, () => onContentResumeRequested(playerState));
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED, () => onAllAdsCompleted(playerState));

        // Detekce dostupnosti reklamy
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.LOADED, () => {
            playerState.hasValidAd = true;
        });

        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.STARTED, () => {
            playerState.hasValidAd = true;

            // Chrome autoplay strategy - unmute po spuštění (jako YouTube detektor)
            if (playerState.videoElement && playerState.videoElement.muted) {
                setTimeout(() => {
                    try {
                        if (playerState.adsManager) {
                            playerState.adsManager.setVolume(1);
                        }
                        if (playerState.videoElement) {
                            playerState.videoElement.muted = false;
                        }
                    } catch (e) {
                        // Ignorujeme chyby při unmute
                    }
                }, 150); // Krátké zpoždění jako v YouTube detektoru
            }

            // Vytvoříme ovládací prvky zvuku po spuštění reklamy
            createVolumeControls(playerState);
        });

        // Detekce chyb při načítání reklamy
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.AD_BREAK_FETCH_ERROR, () => {
            playerState.hasValidAd = false;
            cleanupAdWithoutAd(playerState);
        });

        // Event listenery pro zvuk
        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.VOLUME_CHANGED, () => {
            if (playerState.volumeButton) {
                const currentVolume = playerState.adsManager.getVolume();
                const isMuted = currentVolume === 0;
                playerState.volumeButton.innerHTML = isMuted ? '🔇' : '🔊';
                playerState.isMuted = isMuted;
            }
        });

        playerState.adsManager.addEventListener(google.ima.AdEvent.Type.VOLUME_MUTED, () => {
            if (playerState.volumeButton) {
                playerState.volumeButton.innerHTML = '🔇';
                playerState.isMuted = true;
            }
        });

        try {
            playerState.adsManager.init(playerState.width, playerState.height, google.ima.ViewMode.NORMAL);
            playerState.adsManager.start();
        } catch (adError) {
            error(`Chyba při spuštění AdsManageru pro ${playerState.id}:`, adError);
            cleanupAd(playerState);
        }
    }

    /**
     * Handler pro chyby reklamy
     */
    function onAdError(adErrorEvent, playerState) {
        const adError = adErrorEvent.getError();
        error(`Chyba reklamy pro ${playerState.id}:`, adError);

        // Označíme, že nemáme validní reklamu
        playerState.hasValidAd = false;

        // Pokud je chyba typu "no ads", použijeme speciální cleanup
        if (adError && (adError.getMessage().includes('No ads') ||
                       adError.getMessage().includes('no fill') ||
                       adError.getErrorCode() === 1009)) { // VAST_NO_ADS_AFTER_WRAPPER
            cleanupAdWithoutAd(playerState);
        } else {
            cleanupAd(playerState);
        }
    }

    /**
     * Handler pro dokončení všech reklam
     */
    function onAllAdsCompleted(playerState) {
        cleanupAd(playerState);
    }

    /**
     * Handler pro požadavek na obnovení obsahu
     */
    function onContentResumeRequested(playerState) {
        cleanupAd(playerState);
    }

    /**
     * Vyčistí reklamu a odstraní vše
     */
    function cleanupAd(playerState) {
        // Zničíme AdsManager
        if (playerState.adsManager) {
            playerState.adsManager.destroy();
            playerState.adsManager = null;
        }

        // Odstraníme ovládací prvky zvuku
        if (playerState.volumeButton) {
            playerState.volumeButton.remove();
            playerState.volumeButton = null;
        }

        // Odstraníme ad kontejner
        if (playerState.adContainer) {
            playerState.adContainer.remove();
            playerState.adContainer = null;
        }

        // Odstraníme overlay úplně
        if (playerState.overlay) {
            playerState.overlay.remove();
            playerState.overlay = null;
        }

        // Také zkusíme najít overlay podle data-player-id
        const overlayById = document.querySelector(`[data-player-id="${playerState.id}"]`);
        if (overlayById) {
            overlayById.remove();
        }
    }

    /**
     * Vyčistí přehrávač když není dostupná žádná reklama
     */
    function cleanupAdWithoutAd(playerState) {
        // Zničíme AdsManager
        if (playerState.adsManager) {
            playerState.adsManager.destroy();
            playerState.adsManager = null;
        }

        // Odstraníme ad kontejner
        if (playerState.adContainer) {
            playerState.adContainer.remove();
            playerState.adContainer = null;
        }

        // Odstraníme overlay úplně - žádná reklama není dostupná
        if (playerState.overlay) {
            playerState.overlay.remove();
            playerState.overlay = null;
        }

        // Také zkusíme najít overlay podle data-player-id
        const overlayById = document.querySelector(`[data-player-id="${playerState.id}"]`);
        if (overlayById) {
            overlayById.remove();
        }
    }

    /**
     * Vyčistí retry timer
     */
    function clearRetryTimer() {
        if (retryTimer) {
            clearTimeout(retryTimer);
            retryTimer = null;
        }
    }

    /**
     * Restartuje detekci embedů
     */
    function restartDetection() {
        clearRetryTimer();
        retryCount = 0;
        if (imaLoaded) {
            startRobustEmbedDetection();
        } else {
            checkImaAndProceed();
        }
    }

    // Expose API
    window.InstagramVideoAds = {
        init: init,
        processEmbeds: processEmbeds,
        processEmbedsWithRetry: processEmbedsWithRetry,
        restartDetection: restartDetection,
        clearRetryTimer: clearRetryTimer,

        config: (newConfig) => {
            globalConfig = { ...globalConfig, ...newConfig };
            return globalConfig;
        },
        getPlayers: () => players,
        getRetryCount: () => retryCount,
        isInstagramScriptLoaded: () => instagramScriptLoaded,
        debugInfo: () => {
            const allPotentialEmbeds = document.querySelectorAll(INSTAGRAM_SELECTORS.join(', '));
            const validEmbeds = Array.from(allPotentialEmbeds).filter(embed =>
                isValidInstagramEmbed(embed)
            );

            return {
                potentialEmbeds: allPotentialEmbeds.length,
                validEmbeds: validEmbeds.length,
                playersCreated: players.length,
                retryCount: retryCount,
                maxRetries: globalConfig.maxRetries,
                imaLoaded: imaLoaded,
                instagramScriptLoaded: instagramScriptLoaded,
                config: globalConfig
            };
        },
        forceProcessEmbed: (selector) => {
            // Funkce pro ruční zpracování embedu
            const embed = document.querySelector(selector);
            if (embed) {
                preparePlayerAsync(embed, players.length);
                return true;
            }
            return false;
        },
        findOverlays: () => {
            // Najde všechny overlay elementy
            const overlays = document.querySelectorAll('.instagram-ad-overlay');
            return {
                count: overlays.length,
                overlays: Array.from(overlays).map(o => ({
                    playerId: o.getAttribute('data-player-id'),
                    visible: o.offsetWidth > 0 && o.offsetHeight > 0
                }))
            };
        },
        getPlayerStates: () => {
            // Vrátí stav všech přehrávačů
            return players.map(p => ({
                id: p.id,
                hasValidAd: p.hasValidAd,
                isMuted: p.isMuted,
                isAdPlaying: p.isAdPlaying,
                adRequested: p.adRequested,
                width: p.width,
                height: p.height,
                volume: p.adsManager ? p.adsManager.getVolume() : null
            }));
        }
    };

    // Auto-inicializace
    if (defaultConfig.autoInit) {
        try {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    setTimeout(() => {
                        try {
                            init();
                        } catch (e) {
                            error('Chyba při inicializaci:', e);
                        }
                    }, defaultConfig.initDelay);
                });
            } else {
                setTimeout(() => {
                    try {
                        init();
                    } catch (e) {
                        error('Chyba při inicializaci:', e);
                    }
                }, defaultConfig.initDelay);
            }
        } catch (e) {
            error('Chyba při nastavování auto-inicializace:', e);
        }
    }

})(window, document);
